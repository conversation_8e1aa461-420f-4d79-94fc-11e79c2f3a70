import 'package:flutter/material.dart';
import 'package:flutterdown/utils/app_localizations.dart';

class CustomBottomNavBar extends StatefulWidget {
  final int currentIndex;
  final ValueChanged<int> onTabChanged;

  const CustomBottomNavBar({
    super.key,
    required this.currentIndex,
    required this.onTabChanged,
  });

  @override
  State<CustomBottomNavBar> createState() => _CustomBottomNavBarState();
}

class _CustomBottomNavBarState extends State<CustomBottomNavBar> {
  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      currentIndex: widget.currentIndex,
      onTap: widget.onTabChanged,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Colors.blue,
      unselectedItemColor: Colors.grey,
      items: [
        BottomNavigationBarItem(
          icon: const Icon(Icons.home),
          label: AppLocalizations.of(context)?.home ?? 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.search),
          label: AppLocalizations.of(context)?.search ?? 'بحث',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.download),
          label: AppLocalizations.of(context)?.downloads ?? 'التنزيلات',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.playlist_play),
          label: AppLocalizations.of(context)?.playlists ?? 'قوائم',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.folder),
          label: AppLocalizations.of(context)?.files ?? 'ملفات',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.settings),
          label: AppLocalizations.of(context)?.settings ?? 'الإعدادات',
        ),
      ],
    );
  }
}
