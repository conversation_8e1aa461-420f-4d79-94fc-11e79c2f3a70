import 'package:flutter/material.dart';
import 'package:flutterdown/services/download_service.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutterdown/utils/app_localizations.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _darkMode = false;
  bool _notifications = true;
  String _downloadLocation = '/storage/emulated/0/Download';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.settings),
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // قسم المظهر
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppLocalizations.of(context)!.settings,
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('الوضع الليلي'),
                    value: _darkMode,
                    onChanged: (value) {
                      setState(() {
                        _darkMode = value;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // قسم التنزيلات
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppLocalizations.of(context)!.downloads,
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    title: const Text('موقع التنزيل'),
                    subtitle: Text(_downloadLocation),
                    trailing: const Icon(Icons.folder),
                    onTap: () {
                      // هنا يمكنك إضافة منطق لتغيير موقع التنزيل
                      Fluttertoast.showToast(
                        msg: 'سيتم تطبيق الإعداد عند إعادة تشغيل التطبيق',
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.BOTTOM,
                        backgroundColor: Colors.blue,
                        textColor: Colors.white,
                      );
                    },
                  ),
                  SwitchListTile(
                    title: const Text('إشعارات التنزيل'),
                    value: _notifications,
                    onChanged: (value) {
                      setState(() {
                        _notifications = value;
                      });
                    },
                    subtitle: const Text('إظهار إشعارات عند اكتمال التنزيل'),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // قسم حول
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'حول التطبيق',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    title: const Text('الإصدار'),
                    subtitle: const Text('1.0.0'),
                  ),
                  ListTile(
                    title: const Text('شروط الاستخدام'),
                    onTap: () {
                      _launchURL('https://www.example.com/terms');
                    },
                  ),
                  ListTile(
                    title: const Text('سياسة الخصوصية'),
                    onTap: () {
                      _launchURL('https://www.example.com/privacy');
                    },
                  ),
                  ListTile(
                    title: const Text('تواصل معنا'),
                    onTap: () {
                      _launchURL('mailto:<EMAIL>');
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _launchURL(String url) async {
    if (!await launchUrl(Uri.parse(url))) {
      Fluttertoast.showToast(
        msg: 'لا يمكن فتح الرابط',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }
}
