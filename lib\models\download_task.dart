
import 'package:flutterdown/models/download_status.dart';
import 'package:flutterdown/models/video.dart';
import 'package:flutterdown/models/video_quality.dart';
import 'package:flutterdown/models/audio_quality.dart';

class _DownloadTask {
  String id;
  Video video;
  DownloadTaskStatus status;
  int progress;
  String savePath;
  VideoQuality? videoQuality;
  AudioQuality? audioQuality;

  _DownloadTask({
    required this.id,
    required this.video,
    required this.status,
    required this.progress,
    required this.savePath,
    this.videoQuality,
    this.audioQuality,
  });
}
