import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart' as downloader;
import 'package:path_provider/path_provider.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart' as yt;
import 'package:dio/dio.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutterdown/models/video_quality.dart' as my_quality;
import 'package:flutterdown/models/audio_quality.dart';
import 'package:flutterdown/models/download_status.dart' as my_status;
import 'package:flutterdown/models/download_task.dart';
import 'package:flutterdown/models/download_type.dart';
import 'package:uuid/uuid.dart';
import 'package:path/path.dart' as path;

// استخدام الاسم المستعار لتجنب التعارضات
typedef MyVideoQuality = my_quality.VideoQuality;

class DownloadService extends ChangeNotifier {
  List<_DownloadTask> _tasks = [];
  bool _isDownloading = false;
  String _errorMessage = '';
  String _successMessage = '';

  List<_DownloadTask> get tasks => _tasks;
  bool get isDownloading => _isDownloading;
  String get errorMessage => _errorMessage;
  String get successMessage => _successMessage;

  DownloadService() {
    _initDownloader();
  }

  Future<void> _initDownloader() async {
    await downloader.FlutterDownloader.initialize(debug: true);
    downloader.FlutterDownloader.registerCallback(_downloadCallback);
  }

  static void _downloadCallback(String id, int status, int progress) {
    // يمكن استخدام هذه الدالة لتحديث حالة التنزيل في واجهة المستخدم
  }

  Future<void> requestStoragePermission() async {
    try {
      // محاولة طلب صلاحيات التخزين العامة أولاً
      if (await Permission.storage.isGranted) {
        return;
      }

      var status = await Permission.storage.request();
      if (status.isGranted) {
        return;
      }

      // إذا لم تنجح، جرب صلاحيات الوسائط المحددة لأندرويد 13+
      if (Platform.isAndroid) {
        final videoStatus = await Permission.videos.request();
        final audioStatus = await Permission.audio.request();

        if (videoStatus.isGranted && audioStatus.isGranted) {
          return;
        }
      }

      // إذا فشلت جميع المحاولات
      Fluttertoast.showToast(
        msg: 'يجب منح إذن التخزين للتنزيل',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      throw Exception('تم رفض إذن التخزين');
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'خطأ في طلب الصلاحيات: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      throw Exception('خطأ في طلب الصلاحيات: ${e.toString()}');
    }
  }

  Future<String> getStorageDirectory() async {
    try {
      // محاولة الحصول على مجلد التخزين الخارجي أولاً
      Directory? directory = await getExternalStorageDirectory();
      if (directory != null && await directory.exists()) {
        return directory.path;
      }

      // إذا فشل، استخدم مجلد التطبيق الخاص
      directory = await getApplicationDocumentsDirectory();
      if (directory != null) {
        // إنشاء مجلد خاص بالتنزيلات
        final downloadDir = Directory('${directory.path}/Downloads');
        if (!await downloadDir.exists()) {
          await downloadDir.create(recursive: true);
        }
        return downloadDir.path;
      }

      throw Exception('لا يمكن الوصول إلى مجلد التخزين');
    } catch (e) {
      throw Exception('خطأ في الحصول على مسار التخزين: ${e.toString()}');
    }
  }

  Future<void> downloadVideo(
    yt.Video video, {
    my_quality.VideoQuality? videoQuality,
  }) async {
    try {
      _isDownloading = true;
      _errorMessage = '';
      notifyListeners();

      // طلب إذن التخزين
      await requestStoragePermission();

      // الحصول على مسار التخزين
      final directory = await getStorageDirectory();
      final String fileName = video.title
          .replaceAll(RegExp(r'[^\w\s-]'), '')
          .replaceAll(' ', '_');
      final String savePath = '$directory/$fileName.mp4';

      // التحقق من وجود الملف مسبقًا
      if (await File(savePath).exists()) {
        Fluttertoast.showToast(
          msg: 'الملف موجود بالفعل',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.orange,
          textColor: Colors.white,
        );
        _isDownloading = false;
        notifyListeners();
        return;
      }

      // إنشاء مهمة تنزيل جديدة
      final task = _DownloadTask(
        id: const Uuid().v4(),
        video: video,
        status: my_status.DownloadTaskStatus.running,
        progress: 0,
        savePath: savePath,
        videoQuality: videoQuality,
      );

      _tasks.add(task);
      notifyListeners();

      // استخدام Dio للتنزيل المباشر
      final dio = Dio();

      // استخدام youtube_explode لاستخراج رابط الفيديو بالجودة المحددة
      final youtubeExplode = yt.YoutubeExplode();

      try {
        // الحصول على معلومات الفيديو
        final videoDetails = await youtubeExplode.videos.get(video.id);

        // الحصول على دفق الفيديو
        yt.StreamManifest manifest = await youtubeExplode.videos.streams
            .getManifest(video.id);
        var streamList = manifest.muxed.withHighestBitrate();

        if (streamList == null) {
          throw Exception('لا توجد تدفقات فيديو متاحة');
        }

        var selectedStream = streamList;

        if (videoQuality != null) {
          // محاولة العثور على دفق بالجودة المحددة
          final matchingStreams = manifest.muxed.where((stream) {
            return stream.videoQuality.toString().contains(
              videoQuality.resolution,
            );
          }).toList();

          if (matchingStreams.isNotEmpty) {
            selectedStream = matchingStreams.first;
          }
        }

        // الحصول على رابط الدفق
        final videoUrl = selectedStream.url;

        final response = await dio.get(
          videoUrl.toString(),
          options: Options(
            responseType: ResponseType.stream,
            receiveTimeout: const Duration(seconds: 30),
            sendTimeout: const Duration(seconds: 30),
          ),
        );

        final file = File(savePath);
        final sink = file.openWrite();

        int totalBytes = 0;
        int downloadedBytes = 0;

        response.data.stream.listen(
          (data) {
            downloadedBytes += data.length as int;
            totalBytes = totalBytes > 0 ? totalBytes : downloadedBytes;
            int progress = (downloadedBytes / totalBytes * 100).round();

            // تحديث حالة التنزيل
            task.progress = progress;
            task.status = progress == 100
                ? my_status.DownloadTaskStatus.complete
                : my_status.DownloadTaskStatus.running;
            notifyListeners();

            sink.add(data);
          },
          onDone: () async {
            await sink.close();
            _isDownloading = false;
            _successMessage = 'تم تنزيل الفيديو بنجاح';
            notifyListeners();

            Fluttertoast.showToast(
              msg: 'تم تنزيل الفيديو بنجاح',
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
              backgroundColor: Colors.green,
              textColor: Colors.white,
            );
          },
          onError: (error) {
            sink.close();
            _isDownloading = false;
            _errorMessage = 'حدث خطأ أثناء التنزيل: ${error.toString()}';
            notifyListeners();

            Fluttertoast.showToast(
              msg: 'حدث خطأ أثناء التنزيل',
              toastLength: Toast.LENGTH_LONG,
              gravity: ToastGravity.BOTTOM,
              backgroundColor: Colors.red,
              textColor: Colors.white,
            );
          },
        );
      } catch (e) {
        _isDownloading = false;
        _errorMessage = 'حدث خطأ أثناء معالجة الفيديو: ${e.toString()}';
        notifyListeners();

        Fluttertoast.showToast(
          msg: 'حدث خطأ أثناء معالجة الفيديو',
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      } finally {
        youtubeExplode.close();
      }
    } catch (e) {
      _isDownloading = false;
      _errorMessage = 'حدث خطأ: ${e.toString()}';
      notifyListeners();

      Fluttertoast.showToast(
        msg: 'حدث خطأ: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  Future<void> downloadAudio(
    yt.Video video, {
    AudioQuality? audioQuality,
  }) async {
    try {
      _isDownloading = true;
      notifyListeners();

      // طلب إذن التخزين
      await requestStoragePermission();

      // الحصول على مسار التخزين
      final directory = await getStorageDirectory();
      final String fileName = video.title
          .replaceAll(RegExp(r'[^\w\s-]'), '')
          .replaceAll(' ', '_');
      final String savePath = '$directory/$fileName.mp3';

      // التحقق من وجود الملف مسبقًا
      if (await File(savePath).exists()) {
        Fluttertoast.showToast(
          msg: 'الملف الصوتي موجود بالفعل',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.orange,
          textColor: Colors.white,
        );
        _isDownloading = false;
        notifyListeners();
        return;
      }

      // إنشاء مهمة تنزيل جديدة
      final task = _DownloadTask(
        id: const Uuid().v4(),
        video: video,
        status: my_status.DownloadTaskStatus.running,
        progress: 0,
        savePath: savePath,
        audioQuality: audioQuality,
      );

      _tasks.add(task);
      notifyListeners();

      // استخدام youtube_explode لاستخراج الصوت
      final youtubeExplode2 = yt.YoutubeExplode();

      try {
        // الحصول على معلومات الفيديو
        final videoDetails = await youtubeExplode2.videos.get(video.id);

        // الحصول على دفق الصوت
        yt.StreamManifest audioManifest = await youtubeExplode2.videos.streams
            .getManifest(video.id);
        var audioStreams = audioManifest.audioOnly.withHighestBitrate();

        if (audioStreams == null) {
          throw Exception('لا توجد تدفقات صوتية متاحة');
        }

        var selectedStream = audioStreams;

        if (audioQuality != null) {
          // محاولة العثور على دفق بالجودة المحددة
          final matchingStreams = audioManifest.audioOnly.where((stream) {
            return stream.bitrate.bitsPerSecond == audioQuality.bitrate;
          }).toList();

          if (matchingStreams.isNotEmpty) {
            selectedStream = matchingStreams.first;
          }
        }

        // الحصول على رابط الدفق
        final audioUrl = selectedStream.url;

        // تنزيل الصوت
        final response = await Dio().get(
          audioUrl.toString(),
          options: Options(
            responseType: ResponseType.stream,
            receiveTimeout: const Duration(seconds: 30),
            sendTimeout: const Duration(seconds: 30),
          ),
        );

        final file = File(savePath);
        final sink = file.openWrite();

        int totalBytes = 0;
        int downloadedBytes = 0;

        response.data.stream.listen(
          (data) {
            downloadedBytes += data.length as int;
            totalBytes = totalBytes > 0 ? totalBytes : downloadedBytes;
            int progress = (downloadedBytes / totalBytes * 100).round();

            // تحديث حالة التنزيل
            task.progress = progress;
            task.status = progress == 100
                ? my_status.DownloadTaskStatus.complete
                : my_status.DownloadTaskStatus.running;
            notifyListeners();

            sink.add(data);
          },
          onDone: () async {
            await sink.close();
            _isDownloading = false;
            _successMessage = 'تم استخراج الصوت بنجاح';
            notifyListeners();

            Fluttertoast.showToast(
              msg: 'تم استخراج الصوت بنجاح',
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
              backgroundColor: Colors.green,
              textColor: Colors.white,
            );
          },
          onError: (error) {
            sink.close();
            _isDownloading = false;
            _errorMessage = 'حدث خطأ أثناء استخراج الصوت: ${error.toString()}';
            notifyListeners();

            Fluttertoast.showToast(
              msg: 'حدث خطأ أثناء استخراج الصوت',
              toastLength: Toast.LENGTH_LONG,
              gravity: ToastGravity.BOTTOM,
              backgroundColor: Colors.red,
              textColor: Colors.white,
            );
          },
        );
      } catch (e) {
        _isDownloading = false;
        _errorMessage = 'حدث خطأ أثناء معالجة الفيديو: ${e.toString()}';
        notifyListeners();

        Fluttertoast.showToast(
          msg: 'حدث خطأ أثناء معالجة الفيديو',
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      } finally {
        youtubeExplode2.close();
      }
    } catch (e) {
      _isDownloading = false;
      _errorMessage = 'حدث خطأ: ${e.toString()}';
      notifyListeners();

      Fluttertoast.showToast(
        msg: 'حدث خطأ: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    }
  }

  void clearMessages() {
    _errorMessage = '';
    _successMessage = '';
    notifyListeners();
  }
}

class _DownloadTask {
  String id;
  yt.Video video;
  my_status.DownloadTaskStatus status;
  int progress;
  String savePath;
  my_quality.VideoQuality? videoQuality;
  AudioQuality? audioQuality;

  _DownloadTask({
    required this.id,
    required this.video,
    required this.status,
    required this.progress,
    required this.savePath,
    this.videoQuality,
    this.audioQuality,
  });
}
