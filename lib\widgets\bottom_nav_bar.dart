import 'package:flutter/material.dart';
import 'package:flutterdown/views/home_screen.dart';
import 'package:flutterdown/views/search_screen.dart';
import 'package:flutterdown/views/downloads_screen.dart';
import 'package:flutterdown/views/settings_screen.dart';
import 'package:flutterdown/views/playlist_screen.dart';
import 'package:flutterdown/views/file_manager_screen.dart';
import 'package:flutterdown/widgets/bottom_navigation_bar.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const SearchScreen(),
    const DownloadsScreen(),
    const PlaylistScreen(),
    const FileManagerScreen(),
    const SettingsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _currentIndex, children: _screens),
      bottomNavigationBar: CustomBottomNavBar(
        currentIndex: _currentIndex,
        onTabChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
      ),
    );
  }
}
