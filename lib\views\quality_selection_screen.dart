import 'package:flutter/material.dart';
import 'package:flutterdown/models/video_quality.dart' as my_quality;
import 'package:flutterdown/models/audio_quality.dart';
import 'package:flutterdown/models/download_type.dart';
import 'package:flutterdown/services/download_service.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart';

class QualitySelectionScreen extends StatefulWidget {
  final Video video;
  final DownloadType downloadType;

  const QualitySelectionScreen({
    super.key,
    required this.video,
    required this.downloadType,
  });

  @override
  State<QualitySelectionScreen> createState() => _QualitySelectionScreenState();
}

class _QualitySelectionScreenState extends State<QualitySelectionScreen> {
  my_quality.VideoQuality? _selectedVideoQuality;
  AudioQuality? _selectedAudioQuality;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // اختيار الجودة الافتراضية
    if (widget.downloadType == DownloadType.video) {
      _selectedVideoQuality = my_quality.VideoQuality.qualities.firstWhere(
        (quality) => quality.id == '720',
        orElse: () => my_quality.VideoQuality.qualities.first,
      );
    } else {
      _selectedAudioQuality = AudioQuality.qualities.firstWhere(
        (quality) => quality.id == '128',
        orElse: () => AudioQuality.qualities.first,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.downloadType == DownloadType.video
              ? 'اختيار جودة الفيديو'
              : 'اختيار جودة الصوت',
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // معلومات الفيديو
          Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      widget.video.thumbnails.highResUrl,
                      height: 120,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    widget.video.title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'بواسطة: ${widget.video.author}',
                    style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ),

          // خيارات الجودة
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.downloadType == DownloadType.video
                        ? 'اختر جودة الفيديو'
                        : 'اختر جودة الصوت',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: widget.downloadType == DownloadType.video
                        ? _buildVideoQualityList()
                        : _buildAudioQualityList(),
                  ),
                  const SizedBox(height: 24),
                  _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          onPressed: _startDownload,
                          child: const Text(
                            'بدء التنزيل',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoQualityList() {
    return ListView.builder(
      itemCount: my_quality.VideoQuality.qualities.length,
      itemBuilder: (context, index) {
        final quality = my_quality.VideoQuality.qualities[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: RadioListTile<my_quality.VideoQuality>(
            title: Text('${quality.name} (${quality.resolution})'),
            value: quality,
            groupValue: _selectedVideoQuality,
            onChanged: (value) {
              setState(() {
                _selectedVideoQuality = value;
              });
            },
          ),
        );
      },
    );
  }

  Widget _buildAudioQualityList() {
    return ListView.builder(
      itemCount: AudioQuality.qualities.length,
      itemBuilder: (context, index) {
        final quality = AudioQuality.qualities[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: RadioListTile<AudioQuality>(
            title: Text('${quality.name} (${quality.bitrate} kbps)'),
            value: quality,
            groupValue: _selectedAudioQuality,
            onChanged: (value) {
              setState(() {
                _selectedAudioQuality = value;
              });
            },
          ),
        );
      },
    );
  }

  Future<void> _startDownload() async {
    if (widget.downloadType == DownloadType.video &&
        _selectedVideoQuality == null) {
      Fluttertoast.showToast(
        msg: 'الرجاء اختيار جودة الفيديو',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      return;
    }

    if (widget.downloadType == DownloadType.audio &&
        _selectedAudioQuality == null) {
      Fluttertoast.showToast(
        msg: 'الرجاء اختيار جودة الصوت',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final downloadService = Provider.of<DownloadService>(
        context,
        listen: false,
      );

      if (widget.downloadType == DownloadType.video) {
        await downloadService.downloadVideo(
          widget.video,
          videoQuality: _selectedVideoQuality,
        );
      } else {
        await downloadService.downloadAudio(
          widget.video,
          audioQuality: _selectedAudioQuality,
        );
      }

      Fluttertoast.showToast(
        msg: 'تم بدء عملية التنزيل',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.green,
        textColor: Colors.white,
      );

      Navigator.pop(context);
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'حدث خطأ أثناء التنزيل: ${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
