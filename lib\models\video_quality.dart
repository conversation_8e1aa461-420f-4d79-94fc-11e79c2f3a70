class VideoQuality {
  final String id;
  final String name;
  final String resolution;

  VideoQuality({
    required this.id,
    required this.name,
    required this.resolution,
  });

  static List<VideoQuality> get qualities => [
    VideoQuality(id: '144', name: '144p', resolution: '256x144'),
    VideoQuality(id: '240', name: '240p', resolution: '426x240'),
    VideoQuality(id: '360', name: '360p', resolution: '640x360'),
    VideoQuality(id: '480', name: '480p', resolution: '854x480'),
    VideoQuality(id: '720', name: '720p', resolution: '1280x720'),
    VideoQuality(id: '1080', name: '1080p', resolution: '1920x1080'),
    VideoQuality(id: '1440', name: '1440p', resolution: '2560x1440'),
    VideoQuality(id: '2160', name: '2160p', resolution: '3840x2160'),
  ];
}
