import 'package:flutter/material.dart';
import 'package:flutterdown/views/home_screen.dart';
import 'package:flutterdown/widgets/bottom_nav_bar.dart';
import 'package:provider/provider.dart';
import 'package:flutterdown/services/download_service.dart';
import 'package:flutterdown/utils/app_localizations.dart';
import 'package:flutterdown/utils/app_settings.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  runApp(
    ChangeNotifierProvider(
      create: (context) => DownloadService(),
      child: const MyApp(),
    ),
  );
}

// دالة لطلب الصلاحيات عند الحاجة (Just-in-time)
Future<bool> requestStoragePermission() async {
  try {
    // طلب صلاحيات التخزين العامة فقط
    var status = await Permission.storage.request();

    if (status.isGranted) {
      return true;
    } else if (status.isDenied) {
      // يمكن للمستخدم طلب الصلاحية مرة أخرى
      return false;
    } else if (status.isPermanentlyDenied) {
      // يجب توجيه المستخدم للإعدادات
      await openAppSettings();
      return false;
    }

    return false;
  } catch (e) {
    print('خطأ في طلب صلاحية التخزين: $e');
    return false;
  }
}

// دالة لطلب صلاحيات الوسائط عند الحاجة
Future<bool> requestMediaPermissions() async {
  try {
    // طلب صلاحيات الوسائط المحددة (للأندرويد 13+)
    // هذا كافٍ لحفظ الفيديوهات والصوتيات
    var videoStatus = await Permission.videos.request();
    var audioStatus = await Permission.audio.request();

    return videoStatus.isGranted && audioStatus.isGranted;
  } catch (e) {
    print('خطأ في طلب صلاحيات الوسائط: $e');
    return false;
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'FlutterDown - تحميل الفيديوهات',
      debugShowCheckedModeBanner: false,
      theme: AppSettings.getTheme(context),
      localizationsDelegates: [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ],
      supportedLocales: const [Locale('ar'), Locale('en')],
      locale: const Locale('ar'),
      home: const MainScreen(),
    );
  }
}
