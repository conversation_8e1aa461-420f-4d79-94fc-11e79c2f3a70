
class AudioQuality {
  final String id;
  final String name;
  final int bitrate;

  AudioQuality({
    required this.id,
    required this.name,
    required this.bitrate,
  });

  static List<AudioQuality> get qualities => [
    AudioQuality(id: '128', name: '128kbps', bitrate: 128),
    AudioQuality(id: '160', name: '160kbps', bitrate: 160),
    AudioQuality(id: '192', name: '192kbps', bitrate: 192),
    AudioQuality(id: '256', name: '256kbps', bitrate: 256),
    AudioQuality(id: '320', name: '320kbps', bitrate: 320),
  ];
}
