import 'package:flutter/material.dart';

class AppLocalizations {
  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  // اللغة العربية (الافتراضية)
  String get home => 'الرئيسية';
  String get search => 'بحث';
  String get downloads => 'التنزيلات';
  String get playlists => 'قوائم';
  String get files => 'ملفات';
  String get settings => 'الإعدادات';
  String get downloadVideo => 'تنزيل الفيديو';
  String get downloadAudio => 'تنزيل الصوت';
  String get videoQuality => 'جودة الفيديو';
  String get audioQuality => 'جودة الصوت';
  String get downloading => 'جاري التنزيل...';
  String get downloadComplete => 'اكتمل التنزيل';
  String get downloadFailed => 'فشل التنزيل';
  String get enterVideoUrl => 'أدخل رابط الفيديو';
  String get searchPlaceholder => 'ابحث عن فيديوهات...';
  String get noDownloadsYet => 'لا توجد تنزيلات بعد';
  String get noPlaylistsYet => 'لا توجد قوائم بعد';
  String get noFilesYet => 'لا توجد ملفات بعد';
  String get appName => 'flutterdown';
  String get download => 'تنزيل';
  String get cancel => 'إلغاء';
  String get open => 'فتح';
  String get delete => 'حذف';
  String get share => 'مشاركة';
  String get retry => 'إعادة المحاولة';
  String get permissionDenied => 'تم رفض الإذن';
  String get permissionRequired => 'يتم إذن التخزين للتنزيل';
  String get fileExists => 'الملف موجود بالفعل';
  String get videoDownloaded => 'تم تنزيل الفيديو بنجاح';
  String get audioDownloaded => 'تم استخراج الصوت بنجاح';
  String get errorOccurred => 'حدث خطأ';
  String get downloadingVideo => 'جاري تنزيل الفيديو';
  String get extractingAudio => 'جاري استخراج الصوت';
  String get processingVideo => 'جاري معالجة الفيديو';

  // اللغة الإنجليزية
  Map<String, Map<String, String>> get _localizedValues {
    return {
      'en': {
        'home': 'Home',
        'search': 'Search',
        'downloads': 'Downloads',
        'playlists': 'Playlists',
        'files': 'Files',
        'settings': 'Settings',
        'downloadVideo': 'Download Video',
        'downloadAudio': 'Download Audio',
        'videoQuality': 'Video Quality',
        'audioQuality': 'Audio Quality',
        'downloading': 'Downloading...',
        'downloadComplete': 'Download Complete',
        'downloadFailed': 'Download Failed',
        'enterVideoUrl': 'Enter Video URL',
        'searchPlaceholder': 'Search for videos...',
        'noDownloadsYet': 'No downloads yet',
        'noPlaylistsYet': 'No playlists yet',
        'noFilesYet': 'No files yet',
        'appName': 'FlutterDown',
        'download': 'Download',
        'cancel': 'Cancel',
        'open': 'Open',
        'delete': 'Delete',
        'share': 'Share',
        'retry': 'Retry',
        'permissionDenied': 'Permission Denied',
        'permissionRequired': 'Storage permission is required for downloading',
        'fileExists': 'File already exists',
        'videoDownloaded': 'Video downloaded successfully',
        'audioDownloaded': 'Audio extracted successfully',
        'errorOccurred': 'An error occurred',
        'downloadingVideo': 'Downloading video',
        'extractingAudio': 'Extracting audio',
        'processingVideo': 'Processing video',
      },
      'ar': {
        'home': 'الرئيسية',
        'search': 'بحث',
        'downloads': 'التنزيلات',
        'playlists': 'قوائم',
        'files': 'ملفات',
        'settings': 'الإعدادات',
        'downloadVideo': 'تنزيل الفيديو',
        'downloadAudio': 'تنزيل الصوت',
        'videoQuality': 'جودة الفيديو',
        'audioQuality': 'جودة الصوت',
        'downloading': 'جاري التنزيل...',
        'downloadComplete': 'اكتمل التنزيل',
        'downloadFailed': 'فشل التنزيل',
        'enterVideoUrl': 'أدخل رابط الفيديو',
        'searchPlaceholder': 'ابحث عن فيديوهات...',
        'noDownloadsYet': 'لا توجد تنزيلات بعد',
        'noPlaylistsYet': 'لا توجد قوائم بعد',
        'noFilesYet': 'لا توجد ملفات بعد',
        'appName': 'flutterdown',
        'download': 'تنزيل',
        'cancel': 'إلغاء',
        'open': 'فتح',
        'delete': 'حذف',
        'share': 'مشاركة',
        'retry': 'إعادة المحاولة',
        'permissionDenied': 'تم رفض الإذن',
        'permissionRequired': 'يحتاج التطبيق إلى إذن التخزين للتنزيل',
        'fileExists': 'الملف موجود بالفعل',
        'videoDownloaded': 'تم تنزيل الفيديو بنجاح',
        'audioDownloaded': 'تم استخراج الصوت بنجاح',
        'errorOccurred': 'حدث خطأ',
        'downloadingVideo': 'جاري تنزيل الفيديو',
        'extractingAudio': 'جاري استخراج الصوت',
        'processingVideo': 'جاري معالجة الفيديو',
      }
    };
  }

  String getString(String key, {String locale = 'ar'}) {
    return _localizedValues[locale]?[key] ?? key;
  }

  // دالة للحصول على النص مباشرة بدون تحديد اللغة
  String operator [](String key) {
    return getString(key);
  }
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations();
  }

  @override
  bool shouldReload(LocalizationsDelegate<AppLocalizations> old) => false;
}
